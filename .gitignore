# ChatFlow - Git 忽略文件配置

# ================================
# 环境变量和敏感信息
# ================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Cloudflare Workers 配置备份
chatai-workers/wrangler.toml.bak
chatai-workers/.dev.vars

# API 密钥和配置文件
**/api-keys.json
**/secrets.json
**/*-secrets.*
**/*-keys.*

# ================================
# Node.js 相关
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ================================
# 操作系统相关
# ================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ================================
# 编辑器和IDE
# ================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ================================
# 构建和部署相关
# ================================

# Build outputs
build/
dist/
*.tgz
*.tar.gz

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# ================================
# 测试相关
# ================================

# Jest
coverage/
.nyc_output/

# Cypress
cypress/videos/
cypress/screenshots/

# ================================
# 数据库和存储
# ================================

# SQLite
*.sqlite
*.sqlite3
*.db

# 本地数据文件
data/
uploads/
storage/

# ================================
# 其他
# ================================

# 备份文件
*.bak
*.backup
*.old
*.orig

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.gz

# 临时文件
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db

# 用户自定义忽略文件
.gitignore.local