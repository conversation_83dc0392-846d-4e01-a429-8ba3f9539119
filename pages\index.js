/**
 * ChatFlow 测试页面
 */

import ChatAIWidget from '../components/ChatAI/ChatAIWidget'

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 页面头部 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">ChatFlow</h1>
              <span className="ml-2 text-sm text-gray-500">智能聊天助手</span>
            </div>
          </div>
        </div>
      </header>

      {/* ChatAI 组件 */}
      <ChatAIWidget 
        config={{
          name: '𝒞𝒽𝒶𝓉𝒜𝐼',
          avatar: '/chatai-avatar.png',
          welcomeMessage: '✨ 你好！我是AI助手，。有什么可以帮助你的吗？',
          position: 'bottom-right',
          workerUrl: 'https://your-worker.your-subdomain.workers.dev'
        }}
      />
    </div>
  )
}
