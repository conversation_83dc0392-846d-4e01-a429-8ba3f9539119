/**
 * ChatFlow 测试页面
 * 用于测试头像修改是否正确
 */

import { useState } from 'react'
import ChatAIWidget from '../components/ChatAI/ChatAIWidget'

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 页面头部 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">ChatFlow</h1>
              <span className="ml-2 text-sm text-gray-500">智能聊天助手</span>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                欢迎使用 ChatFlow
              </h2>
              <p className="text-gray-600 mb-6">
                这是一个测试页面，用于验证AI聊天助手的头像是否正确显示。
              </p>
              <div className="space-y-2 text-sm text-gray-500">
                <p>✅ AI助手头像：/chatai-avatar.png</p>
                <p>✅ 用户头像：/user.png</p>
                <p>💬 点击右下角的聊天按钮开始对话</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* ChatAI 组件 */}
      <ChatAIWidget 
        config={{
          name: '𝒞𝒽𝒶𝓉𝒜𝐼',
          avatar: '/chatai-avatar.png',
          welcomeMessage: '✨ 你好！我是AI助手，头像已更新为本地文件。有什么可以帮助你的吗？',
          position: 'bottom-right',
          workerUrl: 'https://chatai-workers.wob21.workers.dev'
        }}
      />
    </div>
  )
}
