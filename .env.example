# ChatFlow 环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# ================================
# 前端配置
# ================================

# ChatAI Workers 服务地址 (必需)
NEXT_PUBLIC_CHATAI_WORKER_URL=https://your-worker.your-subdomain.workers.dev

# ChatAI 基本配置 (可选)
CHATAI_ENABLE=true
CHATAI_NAME=your-application-name
CHATAI_POSITION=bottom-right
CHATAI_AVATAR=/default-chatai.png
CHATAI_WELCOME_MESSAGE=Hello, what can I do for you?

# ================================
# Cloudflare Workers 配置
# ================================

# 环境标识
ENVIRONMENT=development

# CORS 允许的域名 (可选，用逗号分隔)
ALLOWED_ORIGINS=https://your-domain.com,https://localhost:3000

# 文件上传限制 (字节)
MAX_FILE_SIZE=10485760

# ================================
# AI 提供商 API 密钥 (Workers Secrets)
# 注意：这些密钥应该通过 wrangler secret put 命令设置，不要直接写在环境变量文件中
# ================================

# 国外 AI 平台
# OPENAI_API_KEYS=sk-your-openai-key-1,sk-your-openai-key-2
# ANTHROPIC_API_KEYS=sk-ant-your-anthropic-key-1,sk-ant-your-anthropic-key-2
# GEMINI_API_KEYS=your-gemini-key-1,your-gemini-key-2
# GROQ_API_KEYS=gsk-your-groq-key-1,gsk-your-groq-key-2
# MISTRAL_API_KEYS=your-mistral-key-1,your-mistral-key-2
# COHERE_API_KEYS=co-your-cohere-key-1,co-your-cohere-key-2
# PERPLEXITY_API_KEYS=pplx-your-perplexity-key-1,pplx-your-perplexity-key-2
# TOGETHER_API_KEYS=your-together-key-1,your-together-key-2
# FIREWORKS_API_KEYS=fw-your-fireworks-key-1,fw-your-fireworks-key-2
# REPLICATE_API_KEYS=r8-your-replicate-key-1,r8-your-replicate-key-2
# HUGGINGFACE_API_KEYS=hf-your-huggingface-key-1,hf-your-huggingface-key-2

# 国内 AI 平台
# QWEN_API_KEYS=sk-your-qwen-key-1,sk-your-qwen-key-2
# BAIDU_API_KEYS=your-baidu-key-1,your-baidu-key-2
# ZHIPU_API_KEYS=your-zhipu-key-1.your-zhipu-key-2
# MOONSHOT_API_KEYS=sk-your-moonshot-key-1,sk-your-moonshot-key-2
# DEEPSEEK_API_KEYS=sk-your-deepseek-key-1,sk-your-deepseek-key-2
# MINIMAX_API_KEYS=your-minimax-jwt-token-1,your-minimax-jwt-token-2

# ================================
# Cloudflare 资源配置
# ================================

# KV 存储 (用于聊天数据)
# 这些 ID 需要在 wrangler.toml 中配置，不是环境变量
# KV_NAMESPACE_ID=your-kv-namespace-id

# R2 存储 (用于文件上传)
# 这些配置需要在 wrangler.toml 中设置
# R2_BUCKET_NAME=your-r2-bucket-name

# ================================
# 开发调试配置
# ================================

# 日志级别
LOG_LEVEL=info

# 调试模式
DEBUG=false

# 开发模式下的测试密钥 (仅用于开发，不要在生产环境使用)
# DEV_TEST_MODE=true

# ================================
# 安全配置
# ================================

# 速率限制 (每分钟请求数)
RATE_LIMIT_PER_MINUTE=60

# 会话超时时间 (秒)
SESSION_TIMEOUT=3600

# 文件清理间隔 (小时)
CLEANUP_INTERVAL=24

# ================================
# 监控和分析 (可选)
# ================================

# Google Analytics ID
# NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Sentry DSN (错误监控)
# SENTRY_DSN=https://your-sentry-dsn

# ================================
# 部署配置
# ================================

# 部署环境
DEPLOY_ENV=development

# 版本号
APP_VERSION=1.0.0

# 构建时间戳
# BUILD_TIMESTAMP=2024-01-01T00:00:00Z
