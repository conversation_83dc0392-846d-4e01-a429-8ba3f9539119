@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* ChatAI 头像样式 */
.chatai-avatar {
  transition: all 0.3s ease;
}

.chatai-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 确保头像加载失败时的样式 */
.chatai-avatar[src=""],
.chatai-avatar:not([src]) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.chatai-avatar[src=""]:before,
.chatai-avatar:not([src]):before {
  content: "AI";
  font-size: 12px;
}
